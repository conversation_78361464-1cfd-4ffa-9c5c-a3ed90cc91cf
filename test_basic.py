"""
اختبار أساسي لوحدات البرنامج
"""
import sys
import os

def test_imports():
    """اختبار استيراد الوحدات"""
    print("🧪 اختبار استيراد الوحدات...")
    
    try:
        from config import APP_NAME, APP_VERSION
        print(f"   ✅ config.py - {APP_NAME} v{APP_VERSION}")
    except ImportError as e:
        print(f"   ❌ config.py - {e}")
        return False
    
    try:
        from adb_controller import ADBController
        print("   ✅ adb_controller.py")
    except ImportError as e:
        print(f"   ❌ adb_controller.py - {e}")
        return False
    
    try:
        from emulator_manager import EmulatorManager
        print("   ✅ emulator_manager.py")
    except ImportError as e:
        print(f"   ❌ emulator_manager.py - {e}")
        return False
    
    try:
        from apk_manager import APKManager
        print("   ✅ apk_manager.py")
    except ImportError as e:
        print(f"   ❌ apk_manager.py - {e}")
        return False
    
    return True

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("\n🔧 اختبار الوظائف الأساسية...")
    
    try:
        from adb_controller import ADBController
        from emulator_manager import EmulatorManager
        from apk_manager import APKManager
        
        # اختبار ADB Controller
        adb = ADBController()
        print(f"   📱 ADB متاح: {'✅' if adb.check_adb_available() else '❌'}")
        
        # اختبار Emulator Manager
        emulator = EmulatorManager()
        print(f"   🤖 المحاكي متاح: {'✅' if emulator.check_emulator_available() else '❌'}")
        
        # اختبار APK Manager
        apk_manager = APKManager()
        apk_files = apk_manager.get_apk_files()
        print(f"   📦 ملفات APK موجودة: {len(apk_files)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاختبار: {e}")
        return False

def test_directories():
    """اختبار إنشاء المجلدات"""
    print("\n📁 اختبار المجلدات...")
    
    try:
        from config import TEMP_DIR, SCREENSHOTS_DIR, APK_DIR
        
        directories = [
            ("temp", TEMP_DIR),
            ("screenshots", SCREENSHOTS_DIR),
            ("apk_files", APK_DIR)
        ]
        
        for name, path in directories:
            if os.path.exists(path):
                print(f"   ✅ {name}: {path}")
            else:
                print(f"   ❌ {name}: {path} - غير موجود")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص المجلدات: {e}")
        return False

def test_gui_imports():
    """اختبار استيراد مكتبات الواجهة"""
    print("\n🖼️ اختبار مكتبات الواجهة...")
    
    try:
        import tkinter as tk
        from tkinter import ttk, filedialog, messagebox
        print("   ✅ tkinter وجميع الوحدات")
        
        # اختبار إنشاء نافذة بسيطة
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        root.destroy()
        print("   ✅ إنشاء نافذة تجريبية")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في مكتبات الواجهة: {e}")
        return False

def main():
    """الوظيفة الرئيسية للاختبار"""
    print("=" * 50)
    print("   اختبار أساسي لبرنامج APK Runner")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_directories,
        test_basic_functionality,
        test_gui_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للاستخدام")
        print("   يمكنك الآن تشغيل: python main.py")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
