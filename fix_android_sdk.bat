@echo off
chcp 65001 >nul
title حل مشكلة Android SDK

echo ========================================
echo    حل مشكلة Android SDK تلقائياً
echo ========================================
echo.

echo 🔧 هذه الأداة ستساعدك في حل مشكلة Android SDK
echo.
echo الخيارات المتاحة:
echo.
echo 1. الإعداد السريع (مُوصى به للمبتدئين)
echo    - تحميل ADB فقط
echo    - يعمل مع المحاكيات الخارجية
echo    - سريع وبسيط
echo.
echo 2. التثبيت الكامل (للمتقدمين)
echo    - تحميل Android SDK كاملاً
echo    - يتضمن المحاكي المدمج
echo    - يحتاج وقت أطول
echo.
echo 3. فحص الحالة الحالية
echo    - فحص ما هو مثبت
echo    - عرض المشاكل والحلول
echo.

set /p choice="اختر رقم (1-3): "

if "%choice%"=="1" goto quick_setup
if "%choice%"=="2" goto full_setup
if "%choice%"=="3" goto check_status
goto invalid_choice

:quick_setup
echo.
echo 🚀 بدء الإعداد السريع...
echo.
python quick_sdk_setup.py
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في الإعداد السريع
    echo جرب الخيار رقم 2 أو التثبيت اليدوي
) else (
    echo.
    echo ✅ تم الإعداد السريع بنجاح!
    echo.
    echo 📋 الخطوات التالية:
    echo 1. ثبت محاكي خارجي (BlueStacks مُوصى به)
    echo 2. فعل ADB debugging في المحاكي
    echo 3. شغل البرنامج: python main.py
)
goto end

:full_setup
echo.
echo 🔧 بدء التثبيت الكامل...
echo ⚠️ هذا قد يستغرق 10-30 دقيقة حسب سرعة الإنترنت
echo.
set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i not "%confirm%"=="y" goto end

python install_android_sdk.py
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في التثبيت الكامل
    echo جرب التثبيت اليدوي من Android Studio
) else (
    echo.
    echo ✅ تم التثبيت الكامل بنجاح!
    echo يمكنك الآن تشغيل البرنامج: python main.py
)
goto end

:check_status
echo.
echo 🔍 فحص الحالة الحالية...
echo.
python setup_check.py
goto end

:invalid_choice
echo.
echo ❌ خيار غير صحيح
echo.

:end
echo.
echo ========================================
echo للمساعدة الإضافية، راجع ملف:
echo ANDROID_SDK_GUIDE.md
echo ========================================
echo.
pause
