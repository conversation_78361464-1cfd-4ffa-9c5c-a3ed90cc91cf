@echo off
chcp 65001 >nul
title APK Runner - برنامج تشغيل تطبيقات APK

echo ========================================
echo    APK Runner - برنامج تشغيل تطبيقات APK
echo ========================================
echo.

:: فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.7+ من https://python.org
    pause
    exit /b 1
)

:: فحص وجود الملفات المطلوبة
if not exist "main.py" (
    echo خطأ: ملف main.py غير موجود
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo خطأ: ملف requirements.txt غير موجود
    pause
    exit /b 1
)

:: تثبيت المكتبات المطلوبة إذا لم تكن مثبتة
echo جاري فحص المكتبات المطلوبة...
pip install -r requirements.txt --quiet

if errorlevel 1 (
    echo تحذير: قد تكون هناك مشكلة في تثبيت بعض المكتبات
    echo سيتم المحاولة لتشغيل البرنامج...
    echo.
)

:: تشغيل البرنامج
echo جاري تشغيل البرنامج...
echo.
python main.py

:: في حالة إنهاء البرنامج
echo.
echo تم إنهاء البرنامج
pause
