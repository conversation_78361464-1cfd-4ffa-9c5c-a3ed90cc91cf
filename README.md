# برنامج تشغيل تطبيقات APK

برنامج Python لتشغيل وإدارة تطبيقات Android APK على Windows باستخدام محاكي Android.

## الميزات

- 🚀 تشغيل محاكي Android تلقائياً
- 📱 تثبيت ملفات APK بسهولة
- 🗂️ إدارة التطبيقات المثبتة
- 📸 أخذ لقطات شاشة
- 📁 نقل الملفات من وإلى الجهاز
- 🎨 واجهة مستخدم عربية سهلة الاستخدام

## متطلبات النظام

### البرامج المطلوبة:
1. **Python 3.7+**
2. **Android SDK** (يتضمن ADB وأدوات المحاكي)
3. **Windows 10/11**

### مكتبات Python:
```
tkinter (مدمجة مع Python)
Pillow>=9.0.0
requests>=2.28.0
psutil>=5.9.0
```

## التثبيت

### 1. تثبيت Android SDK

#### الطريقة الأولى: Android Studio (الأسهل)
1. حمل وثبت [Android Studio](https://developer.android.com/studio)
2. افتح Android Studio واتبع معالج الإعداد
3. تأكد من تثبيت:
   - Android SDK Platform-Tools
   - Android Emulator
   - System Images (Android 10+ مُوصى به)

#### الطريقة الثانية: Command Line Tools فقط
1. حمل [Command Line Tools](https://developer.android.com/studio#command-tools)
2. استخرج الملفات إلى مجلد (مثل `C:\Android\Sdk`)
3. أضف المسارات التالية إلى متغير البيئة PATH:
   ```
   C:\Android\Sdk\platform-tools
   C:\Android\Sdk\emulator
   C:\Android\Sdk\cmdline-tools\latest\bin
   ```

### 2. تثبيت البرنامج

1. **استنسخ أو حمل الكود:**
   ```bash
   git clone <repository-url>
   cd apk-runner
   ```

2. **ثبت المكتبات المطلوبة:**
   ```bash
   pip install -r requirements.txt
   ```

3. **تحديث إعدادات المسارات:**
   - افتح ملف `config.py`
   - تأكد من صحة مسارات Android SDK:
   ```python
   ANDROID_SDK_PATH = "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk"
   ```

## الاستخدام

### تشغيل البرنامج
```bash
python main.py
```

### الواجهة الرئيسية

#### 1. إطار الحالة
- **حالة المحاكي**: يظهر إذا كان المحاكي يعمل أم لا
- **الجهاز المتصل**: يظهر معرف الجهاز المتصل
- **أزرار التحكم**: تشغيل/إيقاف المحاكي وتحديث الحالة

#### 2. تبويب ملفات APK
- **إضافة APK**: اختر ملفات APK من الكمبيوتر
- **عرض المعلومات**: اسم الملف، الحجم، اسم الحزمة
- **تثبيت**: انقر مزدوجاً أو استخدم زر "تثبيت المحدد"
- **حذف**: إزالة ملفات APK من القائمة

#### 3. تبويب التطبيقات المثبتة
- **عرض التطبيقات**: قائمة بجميع التطبيقات المثبتة
- **تشغيل التطبيق**: تشغيل التطبيق المحدد
- **إلغاء التثبيت**: إزالة التطبيق من الجهاز

#### 4. تبويب الأدوات
- **لقطة الشاشة**: أخذ وحفظ لقطات شاشة
- **نقل الملفات**: نسخ الملفات من وإلى الجهاز
- **سجل الأحداث**: عرض تفاصيل العمليات

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. "لم يتم العثور على ADB"
**الحل:**
- تأكد من تثبيت Android SDK
- تحديث مسار `ANDROID_SDK_PATH` في `config.py`
- إضافة مجلد `platform-tools` إلى PATH

#### 2. "فشل في تشغيل المحاكي"
**الحل:**
- تأكد من تمكين Virtualization في BIOS
- تثبيت Intel HAXM أو AMD Hypervisor
- إنشاء AVD جديد من Android Studio

#### 3. "لا يوجد جهاز متصل"
**الحل:**
- انتظر اكتمال تشغيل المحاكي (قد يستغرق دقائق)
- تشغيل `adb devices` في سطر الأوامر للتحقق
- إعادة تشغيل ADB: `adb kill-server && adb start-server`

#### 4. "فشل في تثبيت APK"
**الحل:**
- تأكد من صحة ملف APK
- فحص مساحة التخزين على الجهاز
- تمكين "Unknown Sources" في إعدادات الجهاز

### فحص الإعدادات

#### تحقق من ADB:
```bash
adb version
adb devices
```

#### تحقق من المحاكي:
```bash
emulator -list-avds
```

## هيكل المشروع

```
apk-runner/
├── main.py              # الملف الرئيسي وواجهة المستخدم
├── config.py            # إعدادات البرنامج
├── adb_controller.py    # وحدة التحكم في ADB
├── emulator_manager.py  # وحدة إدارة المحاكي
├── apk_manager.py       # وحدة إدارة ملفات APK
├── requirements.txt     # المكتبات المطلوبة
├── README.md           # دليل الاستخدام
├── temp/               # ملفات مؤقتة
├── screenshots/        # لقطات الشاشة
└── apk_files/          # ملفات APK المحفوظة
```

## التطوير والتخصيص

### إضافة ميزات جديدة
- عدل الملفات المناسبة حسب الوظيفة
- اتبع نمط الكود الموجود
- أضف التوثيق المناسب

### تخصيص الواجهة
- عدل الألوان في `config.py`
- غير أبعاد النوافذ والخطوط
- أضف أيقونات وصور

## الدعم والمساهمة

### الإبلاغ عن مشاكل
- استخدم سجل الأحداث في البرنامج
- اذكر نظام التشغيل وإصدار Python
- أرفق رسائل الخطأ كاملة

### المساهمة
- Fork المشروع
- أنشئ branch للميزة الجديدة
- اختبر التغييرات
- أرسل Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## إخلاء المسؤولية

- استخدم البرنامج على مسؤوليتك الخاصة
- تأكد من امتلاك حقوق استخدام ملفات APK
- احترم قوانين حقوق الطبع والنشر

---

**ملاحظة**: هذا البرنامج مخصص للأغراض التعليمية والتطويرية. تأكد من الامتثال لجميع القوانين واللوائح المحلية عند استخدامه.
