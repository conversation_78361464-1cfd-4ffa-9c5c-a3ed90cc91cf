"""
إعدادات البرنامج
"""
import os
from pathlib import Path

# مسارات البرنامج
APP_NAME = "APK Runner"
APP_VERSION = "1.0.0"

# مسار Android SDK (يجب تعديله حسب التثبيت)
ANDROID_SDK_PATH = os.path.join(os.path.expanduser("~"), "AppData", "Local", "Android", "Sdk")
ADB_PATH = os.path.join(ANDROID_SDK_PATH, "platform-tools", "adb.exe")
EMULATOR_PATH = os.path.join(ANDROID_SDK_PATH, "emulator", "emulator.exe")
AVDMANAGER_PATH = os.path.join(ANDROID_SDK_PATH, "cmdline-tools", "latest", "bin", "avdmanager.bat")

# إعدادات المحاكي الافتراضية
DEFAULT_AVD_NAME = "APK_Runner_Device"
DEFAULT_API_LEVEL = "30"
DEFAULT_SYSTEM_IMAGE = "system-images;android-30;google_apis;x86_64"

# مجلدات البرنامج
APP_DIR = Path(__file__).parent
TEMP_DIR = APP_DIR / "temp"
SCREENSHOTS_DIR = APP_DIR / "screenshots"
APK_DIR = APP_DIR / "apk_files"

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [TEMP_DIR, SCREENSHOTS_DIR, APK_DIR]:
    directory.mkdir(exist_ok=True)

# إعدادات واجهة المستخدم
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
WINDOW_TITLE = f"{APP_NAME} v{APP_VERSION}"

# ألوان الواجهة
COLORS = {
    'primary': '#2196F3',
    'secondary': '#FFC107',
    'success': '#4CAF50',
    'danger': '#F44336',
    'warning': '#FF9800',
    'info': '#00BCD4',
    'light': '#F5F5F5',
    'dark': '#212121'
}

# رسائل الحالة
MESSAGES = {
    'emulator_starting': 'جاري تشغيل المحاكي...',
    'emulator_running': 'المحاكي يعمل',
    'emulator_stopped': 'المحاكي متوقف',
    'apk_installing': 'جاري تثبيت التطبيق...',
    'apk_installed': 'تم تثبيت التطبيق بنجاح',
    'apk_install_failed': 'فشل في تثبيت التطبيق',
    'apk_uninstalling': 'جاري إلغاء تثبيت التطبيق...',
    'apk_uninstalled': 'تم إلغاء تثبيت التطبيق',
    'screenshot_taken': 'تم أخذ لقطة الشاشة',
    'no_device_connected': 'لا يوجد جهاز متصل'
}
