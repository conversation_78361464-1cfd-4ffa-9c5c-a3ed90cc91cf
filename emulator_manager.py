"""
وحدة إدارة محاكي Android
"""
import subprocess
import os
import time
import psutil
from typing import List, Optional
from config import EMULATOR_PATH, AVDMANAGER_PATH, DEFAULT_AVD_NAME, DEFAULT_API_LEVEL

class EmulatorManager:
    def __init__(self):
        self.emulator_path = EMULATOR_PATH
        self.avdmanager_path = AVDMANAGER_PATH
        self.emulator_process = None
        self.current_avd = None
    
    def check_emulator_available(self) -> bool:
        """فحص توفر محاكي Android"""
        return os.path.exists(self.emulator_path)
    
    def check_avdmanager_available(self) -> bool:
        """فحص توفر AVD Manager"""
        return os.path.exists(self.avdmanager_path)
    
    def run_emulator_command(self, command: List[str], timeout: int = 30) -> tuple:
        """تشغيل أمر المحاكي"""
        try:
            full_command = [self.emulator_path] + command
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8'
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "انتهت مهلة الأمر"
        except Exception as e:
            return False, "", str(e)
    
    def run_avdmanager_command(self, command: List[str], timeout: int = 60) -> tuple:
        """تشغيل أمر AVD Manager"""
        try:
            full_command = [self.avdmanager_path] + command
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8'
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "انتهت مهلة الأمر"
        except Exception as e:
            return False, "", str(e)
    
    def get_available_avds(self) -> List[str]:
        """الحصول على قائمة الأجهزة الافتراضية المتاحة"""
        success, output, error = self.run_avdmanager_command(['list', 'avd'])
        avds = []
        
        if success:
            lines = output.split('\n')
            for line in lines:
                if 'Name:' in line:
                    avd_name = line.split('Name:')[1].strip()
                    avds.append(avd_name)
        
        return avds
    
    def create_avd(self, avd_name: str = None, api_level: str = None) -> bool:
        """إنشاء جهاز افتراضي جديد"""
        if not avd_name:
            avd_name = DEFAULT_AVD_NAME
        if not api_level:
            api_level = DEFAULT_API_LEVEL
        
        # فحص إذا كان AVD موجود بالفعل
        existing_avds = self.get_available_avds()
        if avd_name in existing_avds:
            return True
        
        # إنشاء AVD جديد
        command = [
            'create', 'avd',
            '-n', avd_name,
            '-k', f'system-images;android-{api_level};google_apis;x86_64',
            '--force'
        ]
        
        success, output, error = self.run_avdmanager_command(command, timeout=120)
        return success
    
    def start_emulator(self, avd_name: str = None, wait_for_boot: bool = True) -> bool:
        """تشغيل المحاكي"""
        if not avd_name:
            avd_name = DEFAULT_AVD_NAME
        
        # التأكد من وجود AVD
        if avd_name not in self.get_available_avds():
            if not self.create_avd(avd_name):
                return False
        
        # تشغيل المحاكي في عملية منفصلة
        try:
            command = [self.emulator_path, '-avd', avd_name, '-no-audio', '-no-boot-anim']
            self.emulator_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
            )
            self.current_avd = avd_name
            
            if wait_for_boot:
                return self.wait_for_emulator_boot()
            
            return True
            
        except Exception as e:
            print(f"خطأ في تشغيل المحاكي: {e}")
            return False
    
    def wait_for_emulator_boot(self, timeout: int = 120) -> bool:
        """انتظار اكتمال تشغيل المحاكي"""
        from adb_controller import ADBController
        
        adb = ADBController()
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            devices = adb.get_connected_devices()
            for device in devices:
                if device['status'] == 'device':
                    # فحص إذا كان النظام جاهز
                    success, output, _ = adb.run_adb_command([
                        '-s', device['id'], 'shell', 'getprop', 'sys.boot_completed'
                    ])
                    if success and '1' in output.strip():
                        return True
            
            time.sleep(2)
        
        return False
    
    def stop_emulator(self) -> bool:
        """إيقاف المحاكي"""
        if self.emulator_process:
            try:
                # إنهاء العملية بلطف
                self.emulator_process.terminate()
                
                # انتظار لمدة 10 ثوان
                try:
                    self.emulator_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    # إنهاء قسري إذا لم ينته بلطف
                    self.emulator_process.kill()
                
                self.emulator_process = None
                self.current_avd = None
                return True
                
            except Exception as e:
                print(f"خطأ في إيقاف المحاكي: {e}")
                return False
        
        # البحث عن عمليات المحاكي وإنهاؤها
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                if 'emulator' in proc.info['name'].lower():
                    proc.terminate()
            return True
        except Exception:
            return False
    
    def is_emulator_running(self) -> bool:
        """فحص إذا كان المحاكي يعمل"""
        if self.emulator_process:
            return self.emulator_process.poll() is None
        
        # فحص العمليات الجارية
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if 'emulator' in proc.info['name'].lower():
                    return True
        except Exception:
            pass
        
        return False
    
    def get_emulator_status(self) -> str:
        """الحصول على حالة المحاكي"""
        if self.is_emulator_running():
            return "يعمل"
        else:
            return "متوقف"
