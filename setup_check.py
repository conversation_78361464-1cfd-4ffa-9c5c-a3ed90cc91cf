"""
أداة فحص وإعداد متطلبات البرنامج
"""
import os
import sys
import subprocess
# from pathlib import Path  # سيتم استخدامها لاحقاً

def check_python():
    """فحص إصدار Python"""
    print("🐍 فحص Python...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} - مناسب")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} - يتطلب 3.7+")
        return False

def check_pip_packages():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات المطلوبة...")

    required_packages = ['Pillow', 'requests', 'psutil']
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.lower())
            print(f"   ✅ {package} - مثبت")
        except ImportError:
            print(f"   ❌ {package} - غير مثبت")
            missing_packages.append(package)

    if missing_packages:
        print(f"\n📥 تثبيت المكتبات المفقودة...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("   ✅ تم تثبيت جميع المكتبات")
            return True
        except subprocess.CalledProcessError:
            print("   ❌ فشل في تثبيت بعض المكتبات")
            return False

    return True

def find_android_sdk():
    """البحث عن Android SDK"""
    print("\n🤖 البحث عن Android SDK...")

    # المسارات الشائعة لـ Android SDK
    common_paths = [
        os.path.join(os.path.expanduser("~"), "AppData", "Local", "Android", "Sdk"),
        os.path.join(os.path.expanduser("~"), "Android", "Sdk"),
        "C:\\Android\\Sdk",
        "C:\\Program Files\\Android\\Sdk",
        "C:\\Program Files (x86)\\Android\\Sdk"
    ]

    # فحص متغير البيئة
    android_home = os.environ.get('ANDROID_HOME')
    if android_home:
        common_paths.insert(0, android_home)

    android_sdk_root = os.environ.get('ANDROID_SDK_ROOT')
    if android_sdk_root:
        common_paths.insert(0, android_sdk_root)

    for path in common_paths:
        if os.path.exists(path):
            # فحص وجود الملفات المطلوبة
            adb_path = os.path.join(path, "platform-tools", "adb.exe")
            emulator_path = os.path.join(path, "emulator", "emulator.exe")

            if os.path.exists(adb_path):
                print(f"   ✅ وُجد Android SDK في: {path}")
                print(f"   ✅ ADB موجود: {adb_path}")

                if os.path.exists(emulator_path):
                    print(f"   ✅ Emulator موجود: {emulator_path}")
                else:
                    print(f"   ⚠️  Emulator غير موجود: {emulator_path}")

                return path

    print("   ❌ لم يتم العثور على Android SDK")
    return None

def check_adb():
    """فحص ADB"""
    print("\n🔧 فحص ADB...")

    # محاولة استخدام ADB من config.py أولاً
    try:
        from config import ADB_PATH
        if os.path.exists(ADB_PATH):
            result = subprocess.run([ADB_PATH, 'version'], capture_output=True, text=True)
            if result.returncode == 0:
                print("   ✅ ADB يعمل بشكل صحيح (من الإعدادات المحلية)")
                print(f"   📋 المسار: {ADB_PATH}")
                print(f"   📋 الإصدار: {result.stdout.split()[4]}")
                return True
    except:
        pass

    # محاولة استخدام ADB من PATH
    try:
        result = subprocess.run(['adb', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ ADB يعمل بشكل صحيح (من PATH)")
            print(f"   📋 الإصدار: {result.stdout.split()[4]}")
            return True
        else:
            print("   ❌ ADB لا يعمل")
            return False
    except FileNotFoundError:
        print("   ❌ ADB غير موجود في PATH أو الإعدادات المحلية")
        return False

def check_emulator():
    """فحص المحاكي"""
    print("\n📱 فحص المحاكي...")
    try:
        result = subprocess.run(['emulator', '-list-avds'], capture_output=True, text=True)
        if result.returncode == 0:
            avds = result.stdout.strip().split('\n')
            if avds and avds[0]:
                print("   ✅ المحاكي متاح")
                print("   📋 الأجهزة الافتراضية المتاحة:")
                for avd in avds:
                    if avd.strip():
                        print(f"      - {avd.strip()}")
                return True
            else:
                print("   ⚠️  المحاكي متاح لكن لا توجد أجهزة افتراضية")
                print("   💡 يمكن إنشاء جهاز افتراضي من Android Studio")
                return True
        else:
            print("   ❌ المحاكي لا يعمل")
            return False
    except FileNotFoundError:
        print("   ❌ المحاكي غير موجود في PATH")
        return False

def update_config_file(sdk_path):
    """تحديث ملف الإعدادات"""
    print(f"\n⚙️  تحديث ملف config.py...")

    config_file = "config.py"
    if not os.path.exists(config_file):
        print("   ❌ ملف config.py غير موجود")
        return False

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # تحديث مسار SDK
        content = content.replace(
            'ANDROID_SDK_PATH = os.path.join(os.path.expanduser("~"), "AppData", "Local", "Android", "Sdk")',
            f'ANDROID_SDK_PATH = r"{sdk_path}"'
        )

        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"   ✅ تم تحديث مسار SDK إلى: {sdk_path}")
        return True

    except Exception as e:
        print(f"   ❌ فشل في تحديث الملف: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print("=" * 50)
    print("   أداة فحص وإعداد متطلبات برنامج APK Runner")
    print("=" * 50)

    all_good = True

    # فحص Python
    if not check_python():
        all_good = False

    # فحص المكتبات
    if not check_pip_packages():
        all_good = False

    # البحث عن Android SDK
    sdk_path = find_android_sdk()
    if sdk_path:
        # تحديث ملف الإعدادات
        update_config_file(sdk_path)

        # فحص ADB
        if not check_adb():
            all_good = False

        # فحص المحاكي
        if not check_emulator():
            print("   💡 يمكن تشغيل البرنامج بدون محاكي إذا كان لديك جهاز Android متصل")
    else:
        all_good = False
        print("\n📥 لتثبيت Android SDK:")
        print("   1. حمل Android Studio من: https://developer.android.com/studio")
        print("   2. ثبت البرنامج واتبع معالج الإعداد")
        print("   3. تأكد من تثبيت SDK Platform-Tools و Android Emulator")

    print("\n" + "=" * 50)
    if all_good:
        print("🎉 جميع المتطلبات متوفرة! يمكنك تشغيل البرنامج الآن")
        print("   تشغيل: python main.py")
    else:
        print("⚠️  بعض المتطلبات مفقودة. يرجى إصلاح المشاكل أعلاه")
    print("=" * 50)

    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
