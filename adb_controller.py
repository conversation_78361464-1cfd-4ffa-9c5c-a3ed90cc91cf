"""
وحدة التحكم في ADB (Android Debug Bridge)
"""
import subprocess
import os
import time
from typing import List, Dict, Optional
from config import ADB_PATH, TEMP_DIR

class ADBController:
    def __init__(self):
        self.adb_path = ADB_PATH
        self.device_id = None
    
    def check_adb_available(self) -> bool:
        """فحص توفر ADB"""
        return os.path.exists(self.adb_path)
    
    def run_adb_command(self, command: List[str], timeout: int = 30) -> tuple:
        """تشغيل أمر ADB"""
        try:
            full_command = [self.adb_path] + command
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8'
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "انتهت مهلة الأمر"
        except Exception as e:
            return False, "", str(e)
    
    def get_connected_devices(self) -> List[Dict[str, str]]:
        """الحصول على قائمة الأجهزة المتصلة"""
        success, output, error = self.run_adb_command(['devices'])
        devices = []
        
        if success:
            lines = output.strip().split('\n')[1:]  # تجاهل السطر الأول
            for line in lines:
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        devices.append({
                            'id': parts[0],
                            'status': parts[1]
                        })
        return devices
    
    def connect_to_device(self, device_id: str = None) -> bool:
        """الاتصال بجهاز محدد"""
        devices = self.get_connected_devices()
        
        if not devices:
            return False
        
        if device_id:
            for device in devices:
                if device['id'] == device_id and device['status'] == 'device':
                    self.device_id = device_id
                    return True
        else:
            # الاتصال بأول جهاز متاح
            for device in devices:
                if device['status'] == 'device':
                    self.device_id = device['id']
                    return True
        
        return False
    
    def install_apk(self, apk_path: str) -> bool:
        """تثبيت ملف APK"""
        if not self.device_id:
            return False
        
        command = ['-s', self.device_id, 'install', '-r', apk_path]
        success, output, error = self.run_adb_command(command, timeout=120)
        
        return success and 'Success' in output
    
    def uninstall_app(self, package_name: str) -> bool:
        """إلغاء تثبيت تطبيق"""
        if not self.device_id:
            return False
        
        command = ['-s', self.device_id, 'uninstall', package_name]
        success, output, error = self.run_adb_command(command)
        
        return success and 'Success' in output
    
    def get_installed_packages(self) -> List[str]:
        """الحصول على قائمة التطبيقات المثبتة"""
        if not self.device_id:
            return []
        
        command = ['-s', self.device_id, 'shell', 'pm', 'list', 'packages', '-3']
        success, output, error = self.run_adb_command(command)
        
        packages = []
        if success:
            for line in output.strip().split('\n'):
                if line.startswith('package:'):
                    package_name = line.replace('package:', '').strip()
                    packages.append(package_name)
        
        return packages
    
    def take_screenshot(self, save_path: str) -> bool:
        """أخذ لقطة شاشة"""
        if not self.device_id:
            return False
        
        # أخذ لقطة شاشة على الجهاز
        temp_path = '/sdcard/screenshot.png'
        command1 = ['-s', self.device_id, 'shell', 'screencap', '-p', temp_path]
        success1, _, _ = self.run_adb_command(command1)
        
        if not success1:
            return False
        
        # نسخ الملف إلى الكمبيوتر
        command2 = ['-s', self.device_id, 'pull', temp_path, save_path]
        success2, _, _ = self.run_adb_command(command2)
        
        # حذف الملف المؤقت
        command3 = ['-s', self.device_id, 'shell', 'rm', temp_path]
        self.run_adb_command(command3)
        
        return success2
    
    def start_app(self, package_name: str) -> bool:
        """تشغيل تطبيق"""
        if not self.device_id:
            return False
        
        command = ['-s', self.device_id, 'shell', 'monkey', '-p', package_name, '1']
        success, output, error = self.run_adb_command(command)
        
        return success
    
    def push_file(self, local_path: str, remote_path: str) -> bool:
        """نسخ ملف إلى الجهاز"""
        if not self.device_id:
            return False
        
        command = ['-s', self.device_id, 'push', local_path, remote_path]
        success, output, error = self.run_adb_command(command)
        
        return success
    
    def pull_file(self, remote_path: str, local_path: str) -> bool:
        """نسخ ملف من الجهاز"""
        if not self.device_id:
            return False
        
        command = ['-s', self.device_id, 'pull', remote_path, local_path]
        success, output, error = self.run_adb_command(command)
        
        return success
