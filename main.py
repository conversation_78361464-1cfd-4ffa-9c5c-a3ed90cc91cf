"""
برنامج تشغيل تطبيقات APK
الملف الرئيسي وواجهة المستخدم
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from datetime import datetime
# from PIL import Image, ImageTk  # سيتم استخدامها لاحقاً لعرض الأيقونات

# استيراد الوحدات المحلية
from config import *
from adb_controller import ADBController
from emulator_manager import EmulatorManager
from apk_manager import APKManager

class APKRunnerApp:
    def __init__(self, root):
        self.root = root
        self.root.title(WINDOW_TITLE)
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        self.root.configure(bg=COLORS['light'])

        # تهيئة المكونات
        self.adb = ADBController()
        self.emulator = EmulatorManager()
        self.apk_manager = APKManager()

        # متغيرات الحالة
        self.current_device = None
        self.installed_apps = []
        self.apk_files = []

        # إنشاء واجهة المستخدم
        self.create_widgets()
        self.update_status()

        # فحص دوري للحالة
        self.check_status_periodically()

    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        # شريط القوائم
        self.create_menu_bar()

        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إطار الحالة والتحكم
        self.create_status_frame(main_frame)

        # دفتر الملاحظات للتبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # تبويب إدارة APK
        self.create_apk_tab()

        # تبويب التطبيقات المثبتة
        self.create_apps_tab()

        # تبويب الأدوات
        self.create_tools_tab()

        # شريط الحالة
        self.create_status_bar()

    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="إضافة APK", command=self.add_apk_file)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)

        # قائمة المحاكي
        emulator_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المحاكي", menu=emulator_menu)
        emulator_menu.add_command(label="تشغيل المحاكي", command=self.start_emulator)
        emulator_menu.add_command(label="إيقاف المحاكي", command=self.stop_emulator)
        emulator_menu.add_separator()
        emulator_menu.add_command(label="إعدادات المحاكي", command=self.emulator_settings)

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)

    def create_status_frame(self, parent):
        """إنشاء إطار الحالة والتحكم"""
        status_frame = ttk.LabelFrame(parent, text="حالة النظام", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        # معلومات الحالة
        info_frame = ttk.Frame(status_frame)
        info_frame.pack(fill=tk.X)

        # حالة المحاكي
        ttk.Label(info_frame, text="حالة المحاكي:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.emulator_status_label = ttk.Label(info_frame, text="متوقف", foreground=COLORS['danger'])
        self.emulator_status_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        # حالة الاتصال
        ttk.Label(info_frame, text="الجهاز المتصل:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.device_status_label = ttk.Label(info_frame, text="غير متصل", foreground=COLORS['danger'])
        self.device_status_label.grid(row=0, column=3, sticky=tk.W)

        # أزرار التحكم
        control_frame = ttk.Frame(status_frame)
        control_frame.pack(fill=tk.X, pady=(10, 0))

        self.start_emulator_btn = ttk.Button(control_frame, text="تشغيل المحاكي", command=self.start_emulator)
        self.start_emulator_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_emulator_btn = ttk.Button(control_frame, text="إيقاف المحاكي", command=self.stop_emulator)
        self.stop_emulator_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.refresh_btn = ttk.Button(control_frame, text="تحديث", command=self.update_status)
        self.refresh_btn.pack(side=tk.LEFT)

    def create_apk_tab(self):
        """إنشاء تبويب إدارة APK"""
        apk_frame = ttk.Frame(self.notebook)
        self.notebook.add(apk_frame, text="ملفات APK")

        # أزرار الإدارة
        buttons_frame = ttk.Frame(apk_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(buttons_frame, text="إضافة APK", command=self.add_apk_file).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="تحديث القائمة", command=self.refresh_apk_list).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="حذف المحدد", command=self.delete_selected_apk).pack(side=tk.LEFT)

        # قائمة ملفات APK
        self.apk_tree = ttk.Treeview(apk_frame, columns=('name', 'size', 'package'), show='headings', height=10)
        self.apk_tree.heading('name', text='اسم الملف')
        self.apk_tree.heading('size', text='الحجم')
        self.apk_tree.heading('package', text='اسم الحزمة')

        self.apk_tree.column('name', width=300)
        self.apk_tree.column('size', width=100)
        self.apk_tree.column('package', width=200)

        # شريط التمرير
        apk_scrollbar = ttk.Scrollbar(apk_frame, orient=tk.VERTICAL, command=self.apk_tree.yview)
        self.apk_tree.configure(yscrollcommand=apk_scrollbar.set)

        self.apk_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        apk_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=(0, 10))

        # زر التثبيت
        install_frame = ttk.Frame(apk_frame)
        install_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        self.install_btn = ttk.Button(install_frame, text="تثبيت المحدد", command=self.install_selected_apk)
        self.install_btn.pack(side=tk.RIGHT)

        # ربط الأحداث
        self.apk_tree.bind('<Double-1>', self.on_apk_double_click)

    def create_apps_tab(self):
        """إنشاء تبويب التطبيقات المثبتة"""
        apps_frame = ttk.Frame(self.notebook)
        self.notebook.add(apps_frame, text="التطبيقات المثبتة")

        # أزرار الإدارة
        buttons_frame = ttk.Frame(apps_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(buttons_frame, text="تحديث القائمة", command=self.refresh_apps_list).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="تشغيل المحدد", command=self.start_selected_app).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء تثبيت", command=self.uninstall_selected_app).pack(side=tk.LEFT)

        # قائمة التطبيقات
        self.apps_tree = ttk.Treeview(apps_frame, columns=('package',), show='headings', height=15)
        self.apps_tree.heading('package', text='اسم الحزمة')
        self.apps_tree.column('package', width=500)

        # شريط التمرير
        apps_scrollbar = ttk.Scrollbar(apps_frame, orient=tk.VERTICAL, command=self.apps_tree.yview)
        self.apps_tree.configure(yscrollcommand=apps_scrollbar.set)

        self.apps_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        apps_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=(0, 10))

    def create_tools_tab(self):
        """إنشاء تبويب الأدوات"""
        tools_frame = ttk.Frame(self.notebook)
        self.notebook.add(tools_frame, text="أدوات")

        # أدوات لقطة الشاشة
        screenshot_frame = ttk.LabelFrame(tools_frame, text="لقطة الشاشة", padding=10)
        screenshot_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(screenshot_frame, text="أخذ لقطة شاشة", command=self.take_screenshot).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(screenshot_frame, text="فتح مجلد اللقطات", command=self.open_screenshots_folder).pack(side=tk.LEFT)

        # أدوات نقل الملفات
        files_frame = ttk.LabelFrame(tools_frame, text="نقل الملفات", padding=10)
        files_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(files_frame, text="نسخ ملف إلى الجهاز", command=self.push_file).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(files_frame, text="نسخ ملف من الجهاز", command=self.pull_file).pack(side=tk.LEFT)

        # سجل الأحداث
        log_frame = ttk.LabelFrame(tools_frame, text="سجل الأحداث", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk.Label(self.root, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def log_message(self, message: str):
        """إضافة رسالة إلى سجل الأحداث"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

        # تحديث شريط الحالة
        self.status_bar.config(text=message)

    def update_status(self):
        """تحديث حالة النظام"""
        # فحص حالة المحاكي
        if self.emulator.is_emulator_running():
            self.emulator_status_label.config(text="يعمل", foreground=COLORS['success'])
            self.start_emulator_btn.config(state=tk.DISABLED)
            self.stop_emulator_btn.config(state=tk.NORMAL)
        else:
            self.emulator_status_label.config(text="متوقف", foreground=COLORS['danger'])
            self.start_emulator_btn.config(state=tk.NORMAL)
            self.stop_emulator_btn.config(state=tk.DISABLED)

        # فحص الأجهزة المتصلة
        devices = self.adb.get_connected_devices()
        if devices:
            device = devices[0]  # أول جهاز متاح
            self.device_status_label.config(text=f"متصل ({device['id']})", foreground=COLORS['success'])
            self.adb.connect_to_device(device['id'])
            self.current_device = device['id']

            # تفعيل الأزرار
            self.install_btn.config(state=tk.NORMAL)
        else:
            self.device_status_label.config(text="غير متصل", foreground=COLORS['danger'])
            self.current_device = None

            # تعطيل الأزرار
            self.install_btn.config(state=tk.DISABLED)

        # تحديث قوائم البيانات
        self.refresh_apk_list()
        if self.current_device:
            self.refresh_apps_list()

    def check_status_periodically(self):
        """فحص دوري للحالة كل 5 ثوان"""
        self.update_status()
        self.root.after(5000, self.check_status_periodically)

    # وظائف المحاكي
    def start_emulator(self):
        """تشغيل المحاكي"""
        def start_emulator_thread():
            self.log_message("جاري تشغيل المحاكي...")
            self.start_emulator_btn.config(state=tk.DISABLED)

            if self.emulator.start_emulator():
                self.log_message("تم تشغيل المحاكي بنجاح")
            else:
                self.log_message("فشل في تشغيل المحاكي")
                self.start_emulator_btn.config(state=tk.NORMAL)

        threading.Thread(target=start_emulator_thread, daemon=True).start()

    def stop_emulator(self):
        """إيقاف المحاكي"""
        self.log_message("جاري إيقاف المحاكي...")
        if self.emulator.stop_emulator():
            self.log_message("تم إيقاف المحاكي")
        else:
            self.log_message("فشل في إيقاف المحاكي")
        self.update_status()

    def emulator_settings(self):
        """إعدادات المحاكي"""
        messagebox.showinfo("إعدادات المحاكي", "ستتم إضافة إعدادات المحاكي في إصدار لاحق")

    # وظائف إدارة APK
    def add_apk_file(self):
        """إضافة ملف APK"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف APK",
            filetypes=[("ملفات APK", "*.apk"), ("جميع الملفات", "*.*")]
        )

        if file_path:
            try:
                if self.apk_manager.validate_apk_file(file_path):
                    copied_path = self.apk_manager.copy_apk_to_directory(file_path)
                    self.log_message(f"تم إضافة ملف APK: {os.path.basename(copied_path)}")
                    self.refresh_apk_list()
                else:
                    messagebox.showerror("خطأ", "الملف المحدد ليس ملف APK صالح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة الملف: {str(e)}")

    def refresh_apk_list(self):
        """تحديث قائمة ملفات APK"""
        # مسح القائمة الحالية
        for item in self.apk_tree.get_children():
            self.apk_tree.delete(item)

        # إضافة الملفات الجديدة
        self.apk_files = self.apk_manager.get_apk_files()
        for apk_info in self.apk_files:
            self.apk_tree.insert('', tk.END, values=(
                apk_info['name'],
                apk_info['size'],
                apk_info['package_name']
            ))

    def delete_selected_apk(self):
        """حذف ملف APK المحدد"""
        selection = self.apk_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف للحذف")
            return

        item = selection[0]
        values = self.apk_tree.item(item, 'values')
        filename = values[0]

        # البحث عن الملف في القائمة
        file_path = None
        for apk_info in self.apk_files:
            if apk_info['name'] == filename:
                file_path = apk_info['path']
                break

        if file_path:
            result = messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف الملف:\n{filename}")
            if result:
                if self.apk_manager.delete_apk_file(file_path):
                    self.log_message(f"تم حذف الملف: {filename}")
                    self.refresh_apk_list()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف الملف")

    def install_selected_apk(self):
        """تثبيت ملف APK المحدد"""
        if not self.current_device:
            messagebox.showwarning("تحذير", "لا يوجد جهاز متصل")
            return

        selection = self.apk_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف للتثبيت")
            return

        item = selection[0]
        values = self.apk_tree.item(item, 'values')
        filename = values[0]

        # البحث عن الملف في القائمة
        file_path = None
        for apk_info in self.apk_files:
            if apk_info['name'] == filename:
                file_path = apk_info['path']
                break

        if file_path:
            def install_thread():
                self.log_message(f"جاري تثبيت: {filename}")
                self.install_btn.config(state=tk.DISABLED)

                if self.adb.install_apk(file_path):
                    self.log_message(f"تم تثبيت التطبيق بنجاح: {filename}")
                    self.refresh_apps_list()
                else:
                    self.log_message(f"فشل في تثبيت التطبيق: {filename}")

                self.install_btn.config(state=tk.NORMAL)

            threading.Thread(target=install_thread, daemon=True).start()

    def on_apk_double_click(self, event):
        """عند النقر المزدوج على ملف APK"""
        _ = event  # تجاهل متغير event
        self.install_selected_apk()

    # وظائف إدارة التطبيقات
    def refresh_apps_list(self):
        """تحديث قائمة التطبيقات المثبتة"""
        if not self.current_device:
            return

        # مسح القائمة الحالية
        for item in self.apps_tree.get_children():
            self.apps_tree.delete(item)

        # الحصول على التطبيقات المثبتة
        self.installed_apps = self.adb.get_installed_packages()
        for package in self.installed_apps:
            self.apps_tree.insert('', tk.END, values=(package,))

    def start_selected_app(self):
        """تشغيل التطبيق المحدد"""
        if not self.current_device:
            messagebox.showwarning("تحذير", "لا يوجد جهاز متصل")
            return

        selection = self.apps_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار تطبيق للتشغيل")
            return

        item = selection[0]
        package_name = self.apps_tree.item(item, 'values')[0]

        if self.adb.start_app(package_name):
            self.log_message(f"تم تشغيل التطبيق: {package_name}")
        else:
            self.log_message(f"فشل في تشغيل التطبيق: {package_name}")

    def uninstall_selected_app(self):
        """إلغاء تثبيت التطبيق المحدد"""
        if not self.current_device:
            messagebox.showwarning("تحذير", "لا يوجد جهاز متصل")
            return

        selection = self.apps_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار تطبيق لإلغاء تثبيته")
            return

        item = selection[0]
        package_name = self.apps_tree.item(item, 'values')[0]

        result = messagebox.askyesno("تأكيد إلغاء التثبيت",
                                   f"هل تريد إلغاء تثبيت التطبيق:\n{package_name}")
        if result:
            def uninstall_thread():
                self.log_message(f"جاري إلغاء تثبيت: {package_name}")

                if self.adb.uninstall_app(package_name):
                    self.log_message(f"تم إلغاء تثبيت التطبيق: {package_name}")
                    self.refresh_apps_list()
                else:
                    self.log_message(f"فشل في إلغاء تثبيت التطبيق: {package_name}")

            threading.Thread(target=uninstall_thread, daemon=True).start()

    # وظائف الأدوات
    def take_screenshot(self):
        """أخذ لقطة شاشة"""
        if not self.current_device:
            messagebox.showwarning("تحذير", "لا يوجد جهاز متصل")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = os.path.join(SCREENSHOTS_DIR, f"screenshot_{timestamp}.png")

        if self.adb.take_screenshot(screenshot_path):
            self.log_message(f"تم حفظ لقطة الشاشة: {os.path.basename(screenshot_path)}")
            messagebox.showinfo("نجح", f"تم حفظ لقطة الشاشة في:\n{screenshot_path}")
        else:
            self.log_message("فشل في أخذ لقطة الشاشة")

    def open_screenshots_folder(self):
        """فتح مجلد لقطات الشاشة"""
        os.startfile(SCREENSHOTS_DIR)

    def push_file(self):
        """نسخ ملف إلى الجهاز"""
        if not self.current_device:
            messagebox.showwarning("تحذير", "لا يوجد جهاز متصل")
            return

        file_path = filedialog.askopenfilename(title="اختر ملف للنسخ")
        if file_path:
            filename = os.path.basename(file_path)
            remote_path = f"/sdcard/Download/{filename}"

            if self.adb.push_file(file_path, remote_path):
                self.log_message(f"تم نسخ الملف إلى الجهاز: {filename}")
            else:
                self.log_message(f"فشل في نسخ الملف: {filename}")

    def pull_file(self):
        """نسخ ملف من الجهاز"""
        if not self.current_device:
            messagebox.showwarning("تحذير", "لا يوجد جهاز متصل")
            return

        # في الواقع نحتاج لواجهة لاختيار الملف من الجهاز
        # هذا مثال مبسط
        # remote_path = "/sdcard/Download/"  # سيتم استخدامه لاحقاً
        local_path = filedialog.askdirectory(title="اختر مجلد الحفظ")

        if local_path:
            messagebox.showinfo("معلومات", "هذه الوظيفة قيد التطوير")

    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = f"""
{APP_NAME} v{APP_VERSION}

برنامج لتشغيل وإدارة تطبيقات Android APK

المطور: مساعد الذكي الاصطناعي
التاريخ: 2024

الوظائف:
• تشغيل محاكي Android
• تثبيت ملفات APK
• إدارة التطبيقات المثبتة
• أخذ لقطات شاشة
• نقل الملفات

متطلبات النظام:
• Windows 10/11
• Android SDK
• Python 3.7+
        """
        messagebox.showinfo("حول البرنامج", about_text)


def main():
    """الوظيفة الرئيسية"""
    # فحص المتطلبات الأساسية
    adb = ADBController()
    emulator = EmulatorManager()

    if not adb.check_adb_available():
        messagebox.showerror("خطأ",
                           f"لم يتم العثور على ADB في المسار:\n{ADB_PATH}\n\n"
                           "يرجى تثبيت Android SDK أو تحديث المسار في ملف config.py")
        return

    if not emulator.check_emulator_available():
        messagebox.showwarning("تحذير",
                             f"لم يتم العثور على محاكي Android في المسار:\n{EMULATOR_PATH}\n\n"
                             "بعض الوظائف قد لا تعمل بشكل صحيح")

    # إنشاء وتشغيل التطبيق
    root = tk.Tk()
    app = APKRunnerApp(root)  # إنشاء التطبيق

    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("تم إنهاء البرنامج")
    finally:
        # تنظيف الموارد
        if hasattr(app, 'emulator') and app.emulator.is_emulator_running():
            app.emulator.stop_emulator()


if __name__ == "__main__":
    main()
