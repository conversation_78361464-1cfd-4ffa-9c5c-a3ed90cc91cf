"""
فحص الإعداد الحالي للبرنامج
"""
import os
import subprocess

def check_current_adb():
    """فحص ADB الحالي"""
    print("🔧 فحص ADB...")
    
    try:
        from config import ADB_PATH
        print(f"   📋 مسار ADB في الإعدادات: {ADB_PATH}")
        
        if os.path.exists(ADB_PATH):
            print("   ✅ ملف ADB موجود")
            
            # اختبار تشغيل ADB
            try:
                result = subprocess.run([ADB_PATH, 'version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version_line = result.stdout.split('\n')[0]
                    print(f"   ✅ ADB يعمل: {version_line}")
                    
                    # فحص الأجهزة المتصلة
                    devices_result = subprocess.run([ADB_PATH, 'devices'], 
                                                  capture_output=True, text=True, timeout=10)
                    if devices_result.returncode == 0:
                        devices_lines = devices_result.stdout.strip().split('\n')[1:]
                        connected_devices = [line for line in devices_lines if line.strip() and '\t' in line]
                        
                        if connected_devices:
                            print(f"   📱 أجهزة متصلة: {len(connected_devices)}")
                            for device in connected_devices:
                                device_id, status = device.split('\t')
                                print(f"      - {device_id}: {status}")
                        else:
                            print("   📱 لا توجد أجهزة متصلة")
                    
                    return True
                else:
                    print(f"   ❌ ADB لا يعمل: {result.stderr}")
                    return False
                    
            except subprocess.TimeoutExpired:
                print("   ❌ انتهت مهلة ADB")
                return False
            except Exception as e:
                print(f"   ❌ خطأ في تشغيل ADB: {e}")
                return False
        else:
            print("   ❌ ملف ADB غير موجود")
            return False
            
    except ImportError:
        print("   ❌ لا يمكن قراءة إعدادات ADB")
        return False

def check_android_tools_folder():
    """فحص مجلد android_tools"""
    print("\n📁 فحص مجلد android_tools...")
    
    tools_dir = "android_tools"
    if os.path.exists(tools_dir):
        print(f"   ✅ مجلد موجود: {os.path.abspath(tools_dir)}")
        
        platform_tools = os.path.join(tools_dir, "platform-tools")
        if os.path.exists(platform_tools):
            print(f"   ✅ platform-tools موجود")
            
            adb_exe = os.path.join(platform_tools, "adb.exe")
            if os.path.exists(adb_exe):
                print(f"   ✅ adb.exe موجود")
                
                # حساب حجم المجلد
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(platform_tools):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        total_size += os.path.getsize(filepath)
                
                size_mb = total_size / (1024 * 1024)
                print(f"   📊 حجم platform-tools: {size_mb:.1f} MB")
                
                return True
            else:
                print("   ❌ adb.exe غير موجود")
        else:
            print("   ❌ platform-tools غير موجود")
    else:
        print("   ❌ مجلد android_tools غير موجود")
    
    return False

def check_external_emulator_script():
    """فحص سكريبت المحاكي الخارجي"""
    print("\n📱 فحص سكريبت المحاكي الخارجي...")
    
    script_file = "run_external_emulator.bat"
    if os.path.exists(script_file):
        print(f"   ✅ السكريبت موجود: {script_file}")
        return True
    else:
        print(f"   ❌ السكريبت غير موجود: {script_file}")
        return False

def check_program_files():
    """فحص ملفات البرنامج الأساسية"""
    print("\n📋 فحص ملفات البرنامج...")
    
    required_files = [
        "main.py",
        "config.py", 
        "adb_controller.py",
        "emulator_manager.py",
        "apk_manager.py"
    ]
    
    all_present = True
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - مفقود")
            all_present = False
    
    return all_present

def test_basic_import():
    """اختبار استيراد الوحدات الأساسية"""
    print("\n🧪 اختبار استيراد الوحدات...")
    
    modules = [
        ("config", "إعدادات البرنامج"),
        ("adb_controller", "وحدة ADB"),
        ("emulator_manager", "وحدة المحاكي"),
        ("apk_manager", "وحدة APK")
    ]
    
    all_imported = True
    for module_name, description in modules:
        try:
            __import__(module_name)
            print(f"   ✅ {module_name} - {description}")
        except ImportError as e:
            print(f"   ❌ {module_name} - خطأ: {e}")
            all_imported = False
    
    return all_imported

def main():
    """الوظيفة الرئيسية"""
    print("=" * 50)
    print("   فحص الإعداد الحالي لبرنامج APK Runner")
    print("=" * 50)
    
    checks = [
        ("ملفات البرنامج", check_program_files),
        ("استيراد الوحدات", test_basic_import),
        ("مجلد android_tools", check_android_tools_folder),
        ("ADB الحالي", check_current_adb),
        ("سكريبت المحاكي", check_external_emulator_script)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n{'='*15} {check_name} {'='*15}")
        if check_func():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 النتائج: {passed}/{total} فحص نجح")
    
    if passed >= 4:  # معظم الفحوصات نجحت
        print("🎉 الإعداد جيد! يمكنك تشغيل البرنامج")
        print()
        print("📋 للتشغيل:")
        print("   python main.py")
        print()
        print("💡 إذا لم تكن لديك محاكي:")
        print("   1. ثبت BlueStacks أو NoxPlayer")
        print("   2. فعل ADB debugging")
        print("   3. شغل البرنامج")
    else:
        print("⚠️ هناك مشاكل في الإعداد")
        print()
        print("🔧 للإصلاح:")
        print("   python quick_sdk_setup.py")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
