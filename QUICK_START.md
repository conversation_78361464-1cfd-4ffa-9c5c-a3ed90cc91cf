# دليل البدء السريع - برنامج تشغيل APK

## 🚀 البدء السريع

### 1. فحص المتطلبات
```bash
python setup_check.py
```

### 2. تشغيل البرنامج
```bash
python main.py
```
أو انقر مزدوجاً على `run.bat`

## 📋 المتطلبات الأساسية

### ✅ Python 3.7+
- حمل من: https://python.org
- تأكد من إضافة Python إلى PATH

### ✅ Android SDK
**الطريقة السهلة:**
1. حمل [Android Studio](https://developer.android.com/studio)
2. ثب<PERSON> البرنامج
3. افتح Android Studio → More Actions → SDK Manager
4. تأكد من تثبيت:
   - ✅ Android SDK Platform-Tools
   - ✅ Android Emulator
   - ✅ System Images (Android 10+)

## 🎯 الاستخدام السريع

### تشغيل المحاكي
1. اضغط "تشغيل المحاكي"
2. انتظر حتى يظهر "متصل" في حالة الجهاز

### تثبيت APK
1. اذهب لتبويب "ملفات APK"
2. اضغط "إضافة APK"
3. اختر ملف APK
4. انقر مزدوجاً على الملف أو اضغط "تثبيت المحدد"

### إدارة التطبيقات
1. اذهب لتبويب "التطبيقات المثبتة"
2. اضغط "تحديث القائمة"
3. اختر تطبيق واضغط "تشغيل المحدد"

## 🔧 حل المشاكل السريع

### "لم يتم العثور على ADB"
```bash
# أضف هذا إلى PATH:
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools
```

### "فشل في تشغيل المحاكي"
1. تأكد من تمكين Virtualization في BIOS
2. ثبت Intel HAXM من SDK Manager
3. أنشئ AVD جديد من Android Studio

### "لا يوجد جهاز متصل"
1. انتظر اكتمال تشغيل المحاكي (2-5 دقائق)
2. جرب: `adb kill-server && adb start-server`

## 📁 هيكل الملفات

```
📁 apk-runner/
├── 🐍 main.py              # البرنامج الرئيسي
├── ⚙️ config.py            # الإعدادات
├── 🔧 setup_check.py       # فحص المتطلبات
├── 🚀 run.bat              # تشغيل سريع
├── 📖 README.md            # دليل مفصل
├── 📋 requirements.txt     # المكتبات المطلوبة
├── 📁 apk_files/           # ملفات APK
├── 📁 screenshots/         # لقطات الشاشة
└── 📁 temp/                # ملفات مؤقتة
```

## 🎮 اختصارات مفيدة

- **النقر المزدوج على APK** = تثبيت فوري
- **F5** = تحديث القوائم
- **Ctrl+O** = إضافة ملف APK
- **Delete** = حذف APK المحدد

## 📞 الدعم

إذا واجهت مشاكل:
1. شغل `python setup_check.py` أولاً
2. تحقق من سجل الأحداث في البرنامج
3. تأكد من تشغيل البرنامج كمدير إذا لزم الأمر

---
**نصيحة:** احتفظ بنسخة احتياطية من ملفات APK المهمة!
