"""
وحدة إدارة ملفات APK
"""
import os
import shutil
import zipfile
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional
from pathlib import Path
from config import APK_DIR, TEMP_DIR

class APKManager:
    def __init__(self):
        self.apk_directory = APK_DIR
        self.temp_directory = TEMP_DIR
    
    def get_apk_info(self, apk_path: str) -> Dict[str, str]:
        """استخراج معلومات ملف APK"""
        info = {
            'name': os.path.basename(apk_path),
            'size': self._format_file_size(os.path.getsize(apk_path)),
            'package_name': 'غير معروف',
            'version': 'غير معروف',
            'app_name': 'غير معروف'
        }
        
        try:
            # استخراج AndroidManifest.xml
            with zipfile.ZipFile(apk_path, 'r') as apk_zip:
                if 'AndroidManifest.xml' in apk_zip.namelist():
                    manifest_data = apk_zip.read('AndroidManifest.xml')
                    # ملاحظة: AndroidManifest.xml مشفر في ملفات APK
                    # نحتاج لأداة خاصة لقراءته، لكن سنحاول استخراج بعض المعلومات
                    info.update(self._parse_manifest_basic(manifest_data))
        except Exception as e:
            print(f"خطأ في قراءة معلومات APK: {e}")
        
        return info
    
    def _parse_manifest_basic(self, manifest_data: bytes) -> Dict[str, str]:
        """استخراج معلومات أساسية من AndroidManifest"""
        # هذه طريقة مبسطة - في الواقع نحتاج لأداة مثل aapt
        info = {}
        try:
            # البحث عن نص package name في البيانات الخام
            manifest_str = manifest_data.decode('utf-8', errors='ignore')
            
            # البحث عن أنماط شائعة
            if 'package=' in manifest_str:
                start = manifest_str.find('package=') + 9
                end = manifest_str.find('"', start)
                if end > start:
                    info['package_name'] = manifest_str[start:end]
            
        except Exception:
            pass
        
        return info
    
    def _format_file_size(self, size_bytes: int) -> str:
        """تنسيق حجم الملف"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def copy_apk_to_directory(self, source_path: str) -> str:
        """نسخ ملف APK إلى مجلد البرنامج"""
        if not os.path.exists(source_path):
            raise FileNotFoundError(f"الملف غير موجود: {source_path}")
        
        filename = os.path.basename(source_path)
        destination_path = os.path.join(self.apk_directory, filename)
        
        # إذا كان الملف موجود، إضافة رقم
        counter = 1
        base_name, ext = os.path.splitext(filename)
        while os.path.exists(destination_path):
            new_filename = f"{base_name}_{counter}{ext}"
            destination_path = os.path.join(self.apk_directory, new_filename)
            counter += 1
        
        shutil.copy2(source_path, destination_path)
        return destination_path
    
    def get_apk_files(self) -> List[Dict[str, str]]:
        """الحصول على قائمة ملفات APK في المجلد"""
        apk_files = []
        
        if not os.path.exists(self.apk_directory):
            return apk_files
        
        for filename in os.listdir(self.apk_directory):
            if filename.lower().endswith('.apk'):
                file_path = os.path.join(self.apk_directory, filename)
                try:
                    apk_info = self.get_apk_info(file_path)
                    apk_info['path'] = file_path
                    apk_files.append(apk_info)
                except Exception as e:
                    print(f"خطأ في قراءة ملف {filename}: {e}")
        
        return apk_files
    
    def delete_apk_file(self, file_path: str) -> bool:
        """حذف ملف APK"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
        except Exception as e:
            print(f"خطأ في حذف الملف: {e}")
        
        return False
    
    def validate_apk_file(self, file_path: str) -> bool:
        """التحقق من صحة ملف APK"""
        try:
            if not os.path.exists(file_path):
                return False
            
            if not file_path.lower().endswith('.apk'):
                return False
            
            # فحص إذا كان ملف ZIP صالح
            with zipfile.ZipFile(file_path, 'r') as apk_zip:
                # فحص وجود ملفات أساسية
                required_files = ['AndroidManifest.xml', 'classes.dex']
                namelist = apk_zip.namelist()
                
                for required_file in required_files:
                    if required_file not in namelist:
                        return False
                
                return True
                
        except Exception:
            return False
    
    def extract_apk_icon(self, apk_path: str) -> Optional[str]:
        """استخراج أيقونة التطبيق"""
        try:
            icon_path = None
            temp_icon_path = os.path.join(self.temp_directory, 'temp_icon.png')
            
            with zipfile.ZipFile(apk_path, 'r') as apk_zip:
                # البحث عن ملفات الأيقونات
                icon_files = [f for f in apk_zip.namelist() 
                             if 'ic_launcher' in f and f.endswith('.png')]
                
                if icon_files:
                    # اختيار أفضل دقة متاحة
                    icon_files.sort(reverse=True)  # ترتيب حسب الاسم (عادة يتضمن الدقة)
                    
                    with apk_zip.open(icon_files[0]) as icon_file:
                        with open(temp_icon_path, 'wb') as temp_file:
                            temp_file.write(icon_file.read())
                    
                    icon_path = temp_icon_path
            
            return icon_path
            
        except Exception as e:
            print(f"خطأ في استخراج الأيقونة: {e}")
            return None
    
    def get_package_name_from_apk(self, apk_path: str) -> Optional[str]:
        """استخراج اسم الحزمة من ملف APK باستخدام أدوات النظام"""
        try:
            # محاولة استخدام aapt إذا كان متاحاً
            from config import ANDROID_SDK_PATH
            aapt_path = os.path.join(ANDROID_SDK_PATH, "build-tools")
            
            # البحث عن أحدث إصدار من build-tools
            if os.path.exists(aapt_path):
                versions = [d for d in os.listdir(aapt_path) 
                           if os.path.isdir(os.path.join(aapt_path, d))]
                if versions:
                    versions.sort(reverse=True)
                    aapt_exe = os.path.join(aapt_path, versions[0], "aapt.exe")
                    
                    if os.path.exists(aapt_exe):
                        import subprocess
                        result = subprocess.run(
                            [aapt_exe, 'dump', 'badging', apk_path],
                            capture_output=True,
                            text=True
                        )
                        
                        if result.returncode == 0:
                            for line in result.stdout.split('\n'):
                                if line.startswith('package:'):
                                    # استخراج اسم الحزمة
                                    start = line.find("name='") + 6
                                    end = line.find("'", start)
                                    if end > start:
                                        return line[start:end]
            
        except Exception as e:
            print(f"خطأ في استخراج اسم الحزمة: {e}")
        
        return None
