"""
إعداد سريع لـ Android SDK - الملفات الأساسية فقط
"""
import os
import sys
import urllib.request
import zipfile
import subprocess

def download_platform_tools():
    """تحميل Platform Tools (ADB) فقط"""
    print("📥 تحميل Android Platform Tools...")
    
    # إنشاء مجلد محلي
    tools_dir = os.path.join(os.getcwd(), "android_tools")
    os.makedirs(tools_dir, exist_ok=True)
    
    # رابط تحميل Platform Tools
    url = "https://dl.google.com/android/repository/platform-tools_r34.0.5-windows.zip"
    zip_path = os.path.join(tools_dir, "platform-tools.zip")
    
    try:
        print("   جاري التحميل...")
        urllib.request.urlretrieve(url, zip_path)
        print("   ✅ تم التحميل")
        
        # استخراج الملفات
        print("   جاري الاستخراج...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(tools_dir)
        
        # حذف ملف ZIP
        os.remove(zip_path)
        
        platform_tools_path = os.path.join(tools_dir, "platform-tools")
        print(f"   ✅ تم الاستخراج إلى: {platform_tools_path}")
        
        return platform_tools_path
        
    except Exception as e:
        print(f"   ❌ فشل التحميل: {e}")
        return None

def update_config_for_local_tools(tools_path):
    """تحديث config.py لاستخدام الأدوات المحلية"""
    print("⚙️ تحديث إعدادات البرنامج...")
    
    config_file = "config.py"
    if not os.path.exists(config_file):
        print("   ❌ ملف config.py غير موجود")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تحديث مسار ADB
        adb_path = os.path.join(tools_path, "adb.exe").replace('\\', '\\\\')
        
        # البحث عن السطر الحالي وتحديثه
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'ADB_PATH =' in line:
                lines[i] = f'ADB_PATH = r"{adb_path}"'
                break
        
        # كتابة الملف المحدث
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print(f"   ✅ تم تحديث مسار ADB إلى: {adb_path}")
        return True
        
    except Exception as e:
        print(f"   ❌ فشل في تحديث الملف: {e}")
        return False

def test_adb(tools_path):
    """اختبار ADB"""
    print("🔧 اختبار ADB...")
    
    adb_path = os.path.join(tools_path, "adb.exe")
    
    try:
        result = subprocess.run([adb_path, "version"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"   ✅ ADB يعمل: {version_line}")
            return True
        else:
            print("   ❌ ADB لا يعمل")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار ADB: {e}")
        return False

def create_simple_emulator_script():
    """إنشاء سكريبت بسيط لتشغيل محاكي خارجي"""
    print("📱 إنشاء سكريبت محاكي بديل...")
    
    script_content = '''@echo off
echo ========================================
echo    تشغيل محاكي Android خارجي
echo ========================================
echo.
echo هذا السكريبت يساعدك في تشغيل محاكي Android
echo يمكنك استخدام أحد البدائل التالية:
echo.
echo 1. BlueStacks - https://www.bluestacks.com
echo 2. NoxPlayer - https://www.bignox.com
echo 3. LDPlayer - https://www.ldplayer.net
echo 4. MEmu - https://www.memuplay.com
echo.
echo بعد تثبيت أي محاكي، تأكد من تفعيل ADB debugging
echo ثم شغل البرنامج الرئيسي
echo.
pause
'''
    
    try:
        with open("run_external_emulator.bat", 'w', encoding='utf-8') as f:
            f.write(script_content)
        print("   ✅ تم إنشاء run_external_emulator.bat")
        return True
    except Exception as e:
        print(f"   ❌ فشل في إنشاء السكريبت: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print("=" * 50)
    print("   إعداد سريع لـ Android SDK")
    print("=" * 50)
    print()
    print("هذه الأداة ستقوم بـ:")
    print("• تحميل Android Platform Tools (ADB)")
    print("• تحديث إعدادات البرنامج")
    print("• إنشاء سكريبت للمحاكيات الخارجية")
    print()
    
    choice = input("هل تريد المتابعة؟ (y/n): ").lower()
    if choice != 'y':
        print("تم الإلغاء")
        return
    
    # تحميل Platform Tools
    tools_path = download_platform_tools()
    if not tools_path:
        print("❌ فشل في تحميل الأدوات")
        input("اضغط Enter للخروج...")
        return
    
    # تحديث الإعدادات
    if not update_config_for_local_tools(tools_path):
        print("❌ فشل في تحديث الإعدادات")
        input("اضغط Enter للخروج...")
        return
    
    # اختبار ADB
    adb_works = test_adb(tools_path)
    
    # إنشاء سكريبت المحاكي
    create_simple_emulator_script()
    
    print("\n" + "=" * 50)
    if adb_works:
        print("🎉 تم الإعداد بنجاح!")
        print()
        print("📋 الخطوات التالية:")
        print("1. ثبت محاكي Android خارجي (BlueStacks مُوصى به)")
        print("2. فعل ADB debugging في المحاكي")
        print("3. شغل البرنامج: python main.py")
        print()
        print("💡 نصائح:")
        print("• BlueStacks: الإعدادات → المتقدم → Android Debug Bridge")
        print("• NoxPlayer: الإعدادات → عام → Root و ADB debugging")
    else:
        print("⚠️ تم التحميل لكن ADB قد لا يعمل بشكل صحيح")
        print("جرب إعادة تشغيل الكمبيوتر أو تثبيت Android Studio")
    
    print("=" * 50)
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
