"""
أداة تحميل وتثبيت Android SDK تلقائياً
"""
import os
import sys
import urllib.request
import zipfile
import subprocess
from pathlib import Path

# روابط تحميل Android SDK
SDK_URLS = {
    'windows': 'https://dl.google.com/android/repository/commandlinetools-win-9477386_latest.zip',
    'platform-tools': 'https://dl.google.com/android/repository/platform-tools_r34.0.5-windows.zip'
}

def create_sdk_directory():
    """إنشاء مجلد Android SDK"""
    sdk_path = os.path.join(os.path.expanduser("~"), "Android", "Sdk")
    os.makedirs(sdk_path, exist_ok=True)
    print(f"📁 تم إنشاء مجلد SDK: {sdk_path}")
    return sdk_path

def download_file(url, destination):
    """تحميل ملف من الإنترنت"""
    print(f"📥 جاري تحميل: {os.path.basename(destination)}")
    
    def progress_hook(block_num, block_size, total_size):
        downloaded = block_num * block_size
        if total_size > 0:
            percent = min(100, (downloaded * 100) // total_size)
            print(f"\r   التقدم: {percent}%", end='', flush=True)
    
    try:
        urllib.request.urlretrieve(url, destination, progress_hook)
        print(f"\n   ✅ تم التحميل: {os.path.basename(destination)}")
        return True
    except Exception as e:
        print(f"\n   ❌ فشل التحميل: {e}")
        return False

def extract_zip(zip_path, extract_to):
    """استخراج ملف ZIP"""
    print(f"📦 جاري استخراج: {os.path.basename(zip_path)}")
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        print(f"   ✅ تم الاستخراج إلى: {extract_to}")
        return True
    except Exception as e:
        print(f"   ❌ فشل الاستخراج: {e}")
        return False

def setup_command_line_tools(sdk_path):
    """إعداد Command Line Tools"""
    print("\n🔧 إعداد Command Line Tools...")
    
    # تحميل Command Line Tools
    tools_zip = os.path.join(sdk_path, "commandlinetools.zip")
    if not download_file(SDK_URLS['windows'], tools_zip):
        return False
    
    # استخراج الملفات
    if not extract_zip(tools_zip, sdk_path):
        return False
    
    # نقل الملفات إلى المكان الصحيح
    cmdline_tools_path = os.path.join(sdk_path, "cmdline-tools")
    latest_path = os.path.join(cmdline_tools_path, "latest")
    
    # إنشاء المجلدات
    os.makedirs(latest_path, exist_ok=True)
    
    # نقل محتويات cmdline-tools إلى latest
    extracted_path = os.path.join(sdk_path, "cmdline-tools")
    if os.path.exists(extracted_path):
        import shutil
        for item in os.listdir(extracted_path):
            if item != "latest":
                src = os.path.join(extracted_path, item)
                dst = os.path.join(latest_path, item)
                if os.path.isdir(src):
                    if os.path.exists(dst):
                        shutil.rmtree(dst)
                    shutil.move(src, dst)
                else:
                    if os.path.exists(dst):
                        os.remove(dst)
                    shutil.move(src, dst)
    
    # حذف ملف ZIP
    os.remove(tools_zip)
    
    print("   ✅ تم إعداد Command Line Tools")
    return True

def setup_platform_tools(sdk_path):
    """إعداد Platform Tools (ADB)"""
    print("\n🔧 إعداد Platform Tools...")
    
    # تحميل Platform Tools
    tools_zip = os.path.join(sdk_path, "platform-tools.zip")
    if not download_file(SDK_URLS['platform-tools'], tools_zip):
        return False
    
    # استخراج الملفات
    if not extract_zip(tools_zip, sdk_path):
        return False
    
    # حذف ملف ZIP
    os.remove(tools_zip)
    
    print("   ✅ تم إعداد Platform Tools")
    return True

def install_system_image(sdk_path):
    """تثبيت System Image للمحاكي"""
    print("\n🤖 تثبيت System Image...")
    
    sdkmanager_path = os.path.join(sdk_path, "cmdline-tools", "latest", "bin", "sdkmanager.bat")
    
    if not os.path.exists(sdkmanager_path):
        print("   ❌ لم يتم العثور على sdkmanager")
        return False
    
    # قبول التراخيص
    print("   📋 قبول تراخيص Android SDK...")
    try:
        subprocess.run([sdkmanager_path, "--licenses"], 
                      input="y\ny\ny\ny\ny\ny\ny\ny\n", 
                      text=True, check=True)
    except subprocess.CalledProcessError:
        print("   ⚠️ تحذير: قد تحتاج لقبول التراخيص يدوياً")
    
    # تثبيت المكونات الأساسية
    components = [
        "platform-tools",
        "emulator",
        "platforms;android-30",
        "system-images;android-30;google_apis;x86_64",
        "build-tools;30.0.3"
    ]
    
    for component in components:
        print(f"   📦 تثبيت: {component}")
        try:
            result = subprocess.run([sdkmanager_path, component], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"   ✅ تم تثبيت: {component}")
            else:
                print(f"   ⚠️ تحذير: مشكلة في تثبيت {component}")
        except Exception as e:
            print(f"   ❌ خطأ في تثبيت {component}: {e}")
    
    return True

def create_avd(sdk_path):
    """إنشاء جهاز افتراضي"""
    print("\n📱 إنشاء جهاز افتراضي...")
    
    avdmanager_path = os.path.join(sdk_path, "cmdline-tools", "latest", "bin", "avdmanager.bat")
    
    if not os.path.exists(avdmanager_path):
        print("   ❌ لم يتم العثور على avdmanager")
        return False
    
    # إنشاء AVD
    avd_name = "APK_Runner_Device"
    try:
        subprocess.run([
            avdmanager_path, "create", "avd",
            "-n", avd_name,
            "-k", "system-images;android-30;google_apis;x86_64",
            "--force"
        ], input="no\n", text=True, check=True)
        
        print(f"   ✅ تم إنشاء الجهاز الافتراضي: {avd_name}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"   ❌ فشل في إنشاء الجهاز الافتراضي: {e}")
        return False

def update_config_file(sdk_path):
    """تحديث ملف الإعدادات"""
    print(f"\n⚙️ تحديث ملف config.py...")
    
    config_file = "config.py"
    if not os.path.exists(config_file):
        print("   ❌ ملف config.py غير موجود")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تحديث مسار SDK
        old_line = 'ANDROID_SDK_PATH = os.path.join(os.path.expanduser("~"), "AppData", "Local", "Android", "Sdk")'
        new_line = f'ANDROID_SDK_PATH = r"{sdk_path}"'
        
        content = content.replace(old_line, new_line)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"   ✅ تم تحديث مسار SDK إلى: {sdk_path}")
        return True
        
    except Exception as e:
        print(f"   ❌ فشل في تحديث الملف: {e}")
        return False

def add_to_path(sdk_path):
    """إضافة مسارات SDK إلى PATH"""
    print("\n🔧 إضافة مسارات إلى PATH...")
    
    paths_to_add = [
        os.path.join(sdk_path, "platform-tools"),
        os.path.join(sdk_path, "emulator"),
        os.path.join(sdk_path, "cmdline-tools", "latest", "bin")
    ]
    
    current_path = os.environ.get('PATH', '')
    
    for path in paths_to_add:
        if path not in current_path:
            print(f"   📋 يُنصح بإضافة هذا المسار إلى PATH: {path}")
    
    print("   💡 لإضافة المسارات نهائياً:")
    print("      1. افتح 'تحرير متغيرات البيئة للنظام'")
    print("      2. اضغط 'متغيرات البيئة'")
    print("      3. حدد 'Path' واضغط 'تحرير'")
    print("      4. أضف المسارات أعلاه")

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("   أداة تحميل وتثبيت Android SDK تلقائياً")
    print("=" * 60)
    
    # فحص الاتصال بالإنترنت
    print("🌐 فحص الاتصال بالإنترنت...")
    try:
        urllib.request.urlopen('https://www.google.com', timeout=5)
        print("   ✅ الاتصال متاح")
    except:
        print("   ❌ لا يوجد اتصال بالإنترنت")
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء مجلد SDK
    sdk_path = create_sdk_directory()
    
    # تحميل وإعداد المكونات
    steps = [
        ("Command Line Tools", lambda: setup_command_line_tools(sdk_path)),
        ("Platform Tools", lambda: setup_platform_tools(sdk_path)),
        ("System Images", lambda: install_system_image(sdk_path)),
        ("جهاز افتراضي", lambda: create_avd(sdk_path)),
        ("تحديث الإعدادات", lambda: update_config_file(sdk_path))
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        if step_func():
            success_count += 1
        else:
            print(f"⚠️ فشل في: {step_name}")
    
    # إضافة إلى PATH
    add_to_path(sdk_path)
    
    # النتائج النهائية
    print("\n" + "="*60)
    print(f"📊 النتائج: {success_count}/{len(steps)} خطوة نجحت")
    
    if success_count >= 3:  # الحد الأدنى للعمل
        print("🎉 تم تثبيت Android SDK بنجاح!")
        print(f"📁 مسار التثبيت: {sdk_path}")
        print("\n🚀 يمكنك الآن تشغيل البرنامج:")
        print("   python main.py")
    else:
        print("❌ فشل في تثبيت Android SDK")
        print("💡 جرب التثبيت اليدوي من Android Studio")
    
    print("="*60)
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
