# دليل حل مشكلة Android SDK

## 🚨 المشكلة: "لم يتم العثور على Android SDK"

هذه المشكلة تحدث عندما لا يجد البرنامج أدوات Android SDK في المسارات المتوقعة.

## 🛠️ الحلول المتاحة

### الحل الأول: الإعداد التلقائي السريع ⚡

```bash
python quick_sdk_setup.py
```

**ما يفعله:**
- ✅ يحمل Android Platform Tools (ADB) تلقائياً
- ✅ يحدث إعدادات البرنامج
- ✅ ينشئ سكريبت للمحاكيات الخارجية
- ✅ لا يحتاج تثبيت Android Studio

### الحل الثاني: التثبيت الكامل 🔧

```bash
python install_android_sdk.py
```

**ما يفعله:**
- ✅ يحمل Android SDK كاملاً
- ✅ يثبت المحاكي المدمج
- ✅ ينشئ جهاز افتراضي
- ✅ يحدث جميع الإعدادات

### الحل الثالث: التثبيت اليدوي 📋

#### 1. تحميل Android Studio (الأسهل)

1. **تحميل Android Studio:**
   - اذهب إلى: https://developer.android.com/studio
   - حمل النسخة الأحدث لـ Windows

2. **تثبيت Android Studio:**
   - شغل ملف التثبيت
   - اتبع معالج الإعداد
   - تأكد من اختيار "Android SDK" و "Android Virtual Device"

3. **إعداد SDK:**
   - افتح Android Studio
   - اذهب إلى: File → Settings → Appearance & Behavior → System Settings → Android SDK
   - تأكد من تثبيت:
     - ✅ Android SDK Platform-Tools
     - ✅ Android Emulator
     - ✅ Android 10.0 (API 29) أو أحدث

#### 2. تحميل Command Line Tools فقط

1. **تحميل الأدوات:**
   - اذهب إلى: https://developer.android.com/studio#command-tools
   - حمل "Command line tools only" لـ Windows

2. **الاستخراج:**
   ```
   C:\Android\Sdk\cmdline-tools\latest\
   ```

3. **تحميل Platform Tools:**
   - حمل من: https://developer.android.com/studio/releases/platform-tools
   - استخرج إلى: `C:\Android\Sdk\platform-tools\`

## 🔧 تحديث إعدادات البرنامج

بعد تثبيت Android SDK، حدث ملف `config.py`:

```python
# ابحث عن هذا السطر:
ANDROID_SDK_PATH = os.path.join(os.path.expanduser("~"), "AppData", "Local", "Android", "Sdk")

# واستبدله بـ:
ANDROID_SDK_PATH = r"C:\Android\Sdk"  # أو المسار الصحيح لديك
```

## 🎮 استخدام محاكيات خارجية

إذا كنت تفضل استخدام محاكي خارجي:

### BlueStacks (مُوصى به)
1. **التحميل:** https://www.bluestacks.com
2. **التثبيت:** اتبع التعليمات العادية
3. **تفعيل ADB:**
   - الإعدادات → المتقدم → Android Debug Bridge
   - فعل "Enable Android Debug Bridge"

### NoxPlayer
1. **التحميل:** https://www.bignox.com
2. **تفعيل ADB:**
   - الإعدادات → عام
   - فعل "Root" و "USB debugging"

### LDPlayer
1. **التحميل:** https://www.ldplayer.net
2. **تفعيل ADB:**
   - الإعدادات → أخرى
   - فعل "ADB debugging"

## 🔍 فحص التثبيت

### اختبار ADB:
```bash
adb version
adb devices
```

### اختبار المحاكي:
```bash
emulator -list-avds
```

### تشغيل فحص البرنامج:
```bash
python setup_check.py
```

## 🚨 حل المشاكل الشائعة

### "ADB غير موجود في PATH"

**الحل:**
1. أضف هذه المسارات إلى متغير البيئة PATH:
   ```
   C:\Android\Sdk\platform-tools
   C:\Android\Sdk\emulator
   C:\Android\Sdk\cmdline-tools\latest\bin
   ```

2. **كيفية إضافة PATH في Windows:**
   - اضغط Win + R واكتب `sysdm.cpl`
   - تبويب "Advanced" → "Environment Variables"
   - حدد "Path" واضغط "Edit"
   - اضغط "New" وأضف المسارات

### "فشل في تشغيل المحاكي"

**الحل:**
1. **تفعيل Virtualization في BIOS:**
   - أعد تشغيل الكمبيوتر
   - ادخل BIOS (عادة F2 أو Delete)
   - ابحث عن "Virtualization" أو "VT-x"
   - فعله واحفظ

2. **تثبيت Intel HAXM:**
   - من SDK Manager في Android Studio
   - أو حمل من: https://github.com/intel/haxm

### "لا يوجد جهاز متصل"

**الحل:**
1. **انتظر اكتمال التشغيل** (2-5 دقائق)
2. **أعد تشغيل ADB:**
   ```bash
   adb kill-server
   adb start-server
   ```
3. **تحقق من المحاكي:**
   ```bash
   adb devices
   ```

## 📋 الخطوات الموصى بها

### للمبتدئين:
1. شغل `python quick_sdk_setup.py`
2. ثبت BlueStacks
3. فعل ADB في BlueStacks
4. شغل البرنامج

### للمتقدمين:
1. ثبت Android Studio
2. أعد إعداد SDK من SDK Manager
3. أنشئ AVD جديد
4. شغل البرنامج

## 🎯 نصائح مهمة

- ✅ **تأكد من الاتصال بالإنترنت** عند التحميل
- ✅ **شغل البرنامج كمدير** إذا لزم الأمر
- ✅ **أعد تشغيل الكمبيوتر** بعد تثبيت SDK
- ✅ **تحقق من مساحة القرص** (يحتاج 3-5 GB)
- ✅ **عطل برامج الحماية** مؤقتاً إذا منعت التحميل

## 📞 الدعم

إذا استمرت المشاكل:
1. شغل `python setup_check.py` وأرسل النتائج
2. تحقق من سجل الأحداث في البرنامج
3. جرب الحلول البديلة أعلاه

---
**ملاحظة:** معظم المشاكل تُحل بإعادة تشغيل الكمبيوتر بعد التثبيت!
